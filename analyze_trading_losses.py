#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交易亏损分析脚本
分析 trading_decisions.csv 中的亏损特点和规律
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_and_prepare_data():
    """加载和预处理数据"""
    df = pd.read_csv('prediction_results/trading_decisions.csv')

    # 转换时间格式
    df['datetime'] = pd.to_datetime(df['datetime'])
    df['date'] = df['datetime'].dt.date
    df['time'] = df['datetime'].dt.time
    df['hour'] = df['datetime'].dt.hour
    df['minute'] = df['datetime'].dt.minute
    df['weekday'] = df['datetime'].dt.weekday  # 0=Monday, 6=Sunday
    df['weekday_name'] = df['datetime'].dt.day_name()
    df['month'] = df['datetime'].dt.month
    df['day'] = df['datetime'].dt.day

    # 计算每日收益
    daily_portfolio = df.groupby('date')['portfolio_value'].last().reset_index()
    daily_portfolio['daily_return'] = daily_portfolio['portfolio_value'].pct_change()
    daily_portfolio['is_loss'] = daily_portfolio['daily_return'] < 0
    daily_portfolio['weekday'] = pd.to_datetime(daily_portfolio['date']).dt.weekday
    daily_portfolio['weekday_name'] = pd.to_datetime(daily_portfolio['date']).dt.day_name()
    daily_portfolio['month'] = pd.to_datetime(daily_portfolio['date']).dt.month
    daily_portfolio['day'] = pd.to_datetime(daily_portfolio['date']).dt.day

    return df, daily_portfolio

def analyze_transaction_losses(df):
    """分析单笔交易的亏损情况"""
    # 找出买卖对
    buy_sells = []
    current_position = None

    for idx, row in df.iterrows():
        if row['decision'] == 'buy' and current_position is None:
            current_position = {
                'buy_time': row['datetime'],
                'buy_price': row['price'],
                'buy_portfolio': row['portfolio_value']
            }
        elif row['decision'] == 'sell' and current_position is not None:
            transaction = current_position.copy()
            transaction.update({
                'sell_time': row['datetime'],
                'sell_price': row['price'],
                'sell_portfolio': row['portfolio_value'],
                'holding_duration': row['datetime'] - current_position['buy_time'],
                'price_return': (row['price'] - current_position['buy_price']) / current_position['buy_price'],
                'portfolio_return': (row['portfolio_value'] - current_position['buy_portfolio']) / current_position['buy_portfolio']
            })
            buy_sells.append(transaction)
            current_position = None

    if not buy_sells:
        return pd.DataFrame()

    transactions_df = pd.DataFrame(buy_sells)
    transactions_df['is_loss'] = transactions_df['portfolio_return'] < 0

    # 添加时间特征
    transactions_df['buy_hour'] = transactions_df['buy_time'].dt.hour
    transactions_df['sell_hour'] = transactions_df['sell_time'].dt.hour
    transactions_df['buy_weekday'] = transactions_df['buy_time'].dt.weekday
    transactions_df['sell_weekday'] = transactions_df['sell_time'].dt.weekday
    transactions_df['buy_weekday_name'] = transactions_df['buy_time'].dt.day_name()
    transactions_df['sell_weekday_name'] = transactions_df['sell_time'].dt.day_name()
    transactions_df['holding_hours'] = transactions_df['holding_duration'].dt.total_seconds() / 3600

    return transactions_df

def analyze_time_patterns(daily_portfolio, transactions_df):
    """分析时间模式"""
    print("=" * 60)
    print("交易亏损分析报告")
    print("=" * 60)

    # 1. 每日收益分析
    print("\n1. 每日收益分析")
    print("-" * 30)
    total_days = len(daily_portfolio.dropna())
    loss_days = daily_portfolio['is_loss'].sum()
    loss_rate = loss_days / total_days * 100

    print(f"总交易天数: {total_days}")
    print(f"亏损天数: {loss_days}")
    print(f"亏损天数比例: {loss_rate:.1f}%")

    # 2. 星期几的亏损分析
    print("\n2. 星期几的亏损分析")
    print("-" * 30)
    weekday_analysis = daily_portfolio.groupby('weekday_name').agg({
        'is_loss': ['count', 'sum', 'mean'],
        'daily_return': 'mean'
    }).round(4)

    weekday_analysis.columns = ['总天数', '亏损天数', '亏损比例', '平均收益率']
    weekday_analysis['亏损比例'] = weekday_analysis['亏损比例'] * 100
    weekday_analysis['平均收益率'] = weekday_analysis['平均收益率'] * 100

    print(weekday_analysis)

    # 3. 月份分析
    print("\n3. 月份亏损分析")
    print("-" * 30)
    month_analysis = daily_portfolio.groupby('month').agg({
        'is_loss': ['count', 'sum', 'mean'],
        'daily_return': 'mean'
    }).round(4)

    month_analysis.columns = ['总天数', '亏损天数', '亏损比例', '平均收益率']
    month_analysis['亏损比例'] = month_analysis['亏损比例'] * 100
    month_analysis['平均收益率'] = month_analysis['平均收益率'] * 100

    print(month_analysis)

    # 4. 单笔交易分析
    if not transactions_df.empty:
        print("\n4. 单笔交易亏损分析")
        print("-" * 30)
        total_transactions = len(transactions_df)
        loss_transactions = transactions_df['is_loss'].sum()
        transaction_loss_rate = loss_transactions / total_transactions * 100

        print(f"总交易次数: {total_transactions}")
        print(f"亏损交易次数: {loss_transactions}")
        print(f"亏损交易比例: {transaction_loss_rate:.1f}%")

        # 买入时间分析
        print("\n买入时间亏损分析:")
        buy_hour_analysis = transactions_df.groupby('buy_hour').agg({
            'is_loss': ['count', 'sum', 'mean'],
            'portfolio_return': 'mean'
        }).round(4)
        buy_hour_analysis.columns = ['总次数', '亏损次数', '亏损比例', '平均收益率']
        buy_hour_analysis['亏损比例'] = buy_hour_analysis['亏损比例'] * 100
        buy_hour_analysis['平均收益率'] = buy_hour_analysis['平均收益率'] * 100
        print(buy_hour_analysis)

        # 持仓时长分析
        print("\n持仓时长亏损分析:")
        transactions_df['holding_category'] = pd.cut(
            transactions_df['holding_hours'],
            bins=[0, 1, 4, 8, 24, float('inf')],
            labels=['<1小时', '1-4小时', '4-8小时', '8-24小时', '>24小时']
        )

        holding_analysis = transactions_df.groupby('holding_category').agg({
            'is_loss': ['count', 'sum', 'mean'],
            'portfolio_return': 'mean'
        }).round(4)
        holding_analysis.columns = ['总次数', '亏损次数', '亏损比例', '平均收益率']
        holding_analysis['亏损比例'] = holding_analysis['亏损比例'] * 100
        holding_analysis['平均收益率'] = holding_analysis['平均收益率'] * 100
        print(holding_analysis)

def create_visualizations(daily_portfolio, transactions_df):
    """创建可视化图表"""
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))

    # 1. 星期几亏损比例
    weekday_loss = daily_portfolio.groupby('weekday_name')['is_loss'].mean() * 100
    weekday_order = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday']
    weekday_loss = weekday_loss.reindex(weekday_order)

    axes[0, 0].bar(range(len(weekday_loss)), weekday_loss.values, color='red', alpha=0.7)
    axes[0, 0].set_title('各星期几的亏损比例')
    axes[0, 0].set_xlabel('星期几')
    axes[0, 0].set_ylabel('亏损比例 (%)')
    axes[0, 0].set_xticks(range(len(weekday_loss)))
    axes[0, 0].set_xticklabels(['周一', '周二', '周三', '周四', '周五'], rotation=45)

    # 2. 每日收益分布
    axes[0, 1].hist(daily_portfolio['daily_return'].dropna() * 100, bins=20, alpha=0.7, color='blue')
    axes[0, 1].axvline(0, color='red', linestyle='--', label='盈亏平衡线')
    axes[0, 1].set_title('每日收益率分布')
    axes[0, 1].set_xlabel('收益率 (%)')
    axes[0, 1].set_ylabel('频次')
    axes[0, 1].legend()

    # 3. 单笔交易收益分布
    if not transactions_df.empty:
        axes[1, 0].hist(transactions_df['portfolio_return'] * 100, bins=20, alpha=0.7, color='green')
        axes[1, 0].axvline(0, color='red', linestyle='--', label='盈亏平衡线')
        axes[1, 0].set_title('单笔交易收益率分布')
        axes[1, 0].set_xlabel('收益率 (%)')
        axes[1, 0].set_ylabel('频次')
        axes[1, 0].legend()

        # 4. 持仓时长 vs 收益率
        axes[1, 1].scatter(transactions_df['holding_hours'], transactions_df['portfolio_return'] * 100, alpha=0.6)
        axes[1, 1].axhline(0, color='red', linestyle='--', label='盈亏平衡线')
        axes[1, 1].set_title('持仓时长 vs 收益率')
        axes[1, 1].set_xlabel('持仓时长 (小时)')
        axes[1, 1].set_ylabel('收益率 (%)')
        axes[1, 1].legend()
    else:
        axes[1, 0].text(0.5, 0.5, '无交易数据', ha='center', va='center', transform=axes[1, 0].transAxes)
        axes[1, 1].text(0.5, 0.5, '无交易数据', ha='center', va='center', transform=axes[1, 1].transAxes)

    plt.tight_layout()
    plt.savefig('trading_loss_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()

def main():
    """主函数"""
    try:
        # 加载数据
        df, daily_portfolio = load_and_prepare_data()
        transactions_df = analyze_transaction_losses(df)

        # 分析时间模式
        analyze_time_patterns(daily_portfolio, transactions_df)

        # 创建可视化
        create_visualizations(daily_portfolio, transactions_df)

        print("\n" + "=" * 60)
        print("分析完成！图表已保存为 'trading_loss_analysis.png'")
        print("=" * 60)

    except Exception as e:
        print(f"分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
