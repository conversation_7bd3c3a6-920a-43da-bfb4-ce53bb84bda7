#!/usr/bin/env python
# -*- coding: utf-8 -*-

import pandas as pd
import os
import sys
import logging
import subprocess
from pathlib import Path
import shutil

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def prepare_data(input_file, output_file, symbol):
    """
    准备数据以便导入到qlib中

    Args:
        input_file: 输入文件路径
        output_file: 输出文件路径
        symbol: 股票代码
    """
    logger.info(f"读取文件: {input_file}")
    df = pd.read_csv(input_file)

    # 添加symbol列
    df['symbol'] = symbol

    # 重命名列以匹配qlib格式
    column_mapping = {
        'datetime': 'date',
        'openPrice': 'open',
        'highPrice': 'high',
        'lowPrice': 'low',
        'closePrice': 'close',
        'tradeVolume': 'volume',
        'amount': 'amount',
        'averagePrice': 'averageprice',
        'IOPV': 'iopv'
    }

    df = df.rename(columns=column_mapping)

    # 确保日期格式正确
    df['date'] = pd.to_datetime(df['date'], format='%Y%m%d%H%M%S')

    # 按日期排序
    df = df.sort_values(by='date')

    logger.info(f"数据行数: {len(df)}")
    logger.info(f"数据日期范围: {df['date'].min()} 至 {df['date'].max()}")

    # 保存处理后的数据
    logger.info(f"保存处理后的数据到: {output_file}")
    df.to_csv(output_file, index=False)

    return df

def dump_to_qlib(csv_file, qlib_dir, symbol, freq="5min"):
    """
    将CSV数据导入到qlib中

    Args:
        csv_file: CSV文件路径
        qlib_dir: qlib数据目录
        symbol: 股票代码
        freq: 数据频率
    """
    logger.info(f"将数据导入到qlib: {csv_file} -> {qlib_dir}")

    # 清空现有的qlib数据
    logger.info("清空现有的qlib数据...")
    features_dir = os.path.join(qlib_dir, "features")
    calendars_dir = os.path.join(qlib_dir, "calendars")
    instruments_dir = os.path.join(qlib_dir, "instruments")

    # 确保目录存在
    os.makedirs(features_dir, exist_ok=True)
    os.makedirs(calendars_dir, exist_ok=True)
    os.makedirs(instruments_dir, exist_ok=True)

    # 清空特征目录
    for item in os.listdir(features_dir):
        item_path = os.path.join(features_dir, item)
        if os.path.isdir(item_path):
            shutil.rmtree(item_path)

    # 清空日历文件
    for item in os.listdir(calendars_dir):
        item_path = os.path.join(calendars_dir, item)
        if os.path.isfile(item_path):
            os.remove(item_path)

    # 清空股票列表文件
    for item in os.listdir(instruments_dir):
        item_path = os.path.join(instruments_dir, item)
        if os.path.isfile(item_path):
            os.remove(item_path)

    # 使用dump_bin.py脚本导入数据
    cmd = [
        "python", "qlib/scripts/dump_bin.py", "dump_all",
        f"--csv_path={csv_file}",
        f"--qlib_dir={qlib_dir}",
        f"--freq={freq}",
        "--date_field_name=date",
        "--symbol_field_name=symbol",
        "--exclude_fields=symbol"
    ]

    logger.info(f"执行命令: {' '.join(cmd)}")

    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        logger.info("数据导入成功")
        logger.info(result.stdout)
    except subprocess.CalledProcessError as e:
        logger.error(f"数据导入失败: {e}")
        logger.error(e.stdout)
        logger.error(e.stderr)
        return False

    # 更新股票代码
    update_instruments(qlib_dir, symbol)

    # 修复特征目录名称
    fix_feature_directory(qlib_dir, symbol)

    return True

def update_instruments(qlib_dir, symbol):
    """
    更新股票列表文件

    Args:
        qlib_dir: qlib数据目录
        symbol: 股票代码
    """
    logger.info(f"更新股票列表文件: {symbol}")

    instruments_file = os.path.join(qlib_dir, "instruments", "all.txt")

    # 读取instruments文件
    with open(instruments_file, 'r') as f:
        lines = f.readlines()

    # 更新股票代码
    new_lines = []
    for line in lines:
        parts = line.strip().split('\t')
        if len(parts) >= 3:
            new_line = f"{symbol}\t{parts[1]}\t{parts[2]}\n"
            new_lines.append(new_line)

    # 写回instruments文件
    with open(instruments_file, 'w') as f:
        f.writelines(new_lines)

    logger.info(f"股票列表文件更新成功: {instruments_file}")

def fix_feature_directory(qlib_dir, symbol):
    """
    修复特征目录名称

    Args:
        qlib_dir: qlib数据目录
        symbol: 股票代码
    """
    logger.info(f"修复特征目录名称: {symbol}")

    features_dir = os.path.join(qlib_dir, "features")

    # 查找所有特征目录
    for item in os.listdir(features_dir):
        item_path = os.path.join(features_dir, item)
        if os.path.isdir(item_path) and item != symbol:
            # 如果目录名不是symbol，则重命名
            target_path = os.path.join(features_dir, symbol)
            logger.info(f"重命名特征目录: {item_path} -> {target_path}")

            # 如果目标目录已存在，先删除
            if os.path.exists(target_path):
                shutil.rmtree(target_path)

            # 重命名目录
            shutil.move(item_path, target_path)

            logger.info(f"特征目录重命名成功: {target_path}")
            break

    # 检查特征目录是否存在
    symbol_dir = os.path.join(features_dir, symbol)
    if not os.path.exists(symbol_dir):
        logger.error(f"特征目录不存在: {symbol_dir}")
        return False

    return True

def verify_data(qlib_dir, symbol, freq="5min"):
    """
    验证qlib数据

    Args:
        qlib_dir: qlib数据目录
        symbol: 股票代码
        freq: 数据频率
    """
    logger.info(f"验证qlib数据: {qlib_dir}")

    # 检查日历文件
    calendar_file = os.path.join(qlib_dir, "calendars", f"{freq}.txt")
    if not os.path.exists(calendar_file):
        logger.error(f"日历文件不存在: {calendar_file}")
        return False

    with open(calendar_file, 'r') as f:
        calendars = [line.strip() for line in f.readlines()]

    logger.info(f"日历数据总数: {len(calendars)}")
    logger.info(f"日历数据范围: {calendars[0]} 至 {calendars[-1]}")

    # 检查股票列表文件
    instruments_file = os.path.join(qlib_dir, "instruments", "all.txt")
    if not os.path.exists(instruments_file):
        logger.error(f"股票列表文件不存在: {instruments_file}")
        return False

    with open(instruments_file, 'r') as f:
        instruments = f.readlines()

    logger.info(f"股票列表: {instruments}")

    # 检查特征文件
    features_dir = os.path.join(qlib_dir, "features", symbol)
    if not os.path.exists(features_dir):
        logger.error(f"特征目录不存在: {features_dir}")
        return False

    feature_files = [f for f in os.listdir(features_dir) if f.endswith(f"{freq}.bin")]
    logger.info(f"特征数量: {len(feature_files)}")
    logger.info(f"特征列表: {feature_files}")

    return True

def process_stock(stock_config, freq="5min"):
    """
    处理单支股票的数据转换

    Args:
        stock_config: 股票配置字典，包含symbol, input_file, output_file, qlib_dir
        freq: 数据频率
    """
    symbol = stock_config['symbol']
    input_file = stock_config['input_file']
    output_file = stock_config['output_file']
    qlib_dir = stock_config['qlib_dir']

    logger.info(f"开始处理股票 {symbol}")
    logger.info(f"输入文件: {input_file}")
    logger.info(f"输出文件: {output_file}")
    logger.info(f"qlib目录: {qlib_dir}")

    try:
        # 准备数据
        logger.info(f"步骤1: 准备数据 - {symbol}")
        prepare_data(input_file, output_file, symbol)

        # 导入到qlib
        logger.info(f"步骤2: 导入到qlib - {symbol}")
        success = dump_to_qlib(output_file, qlib_dir, symbol, freq)

        if not success:
            logger.error(f"股票 {symbol} 导入qlib失败")
            return False

        # 验证数据
        logger.info(f"步骤3: 验证数据 - {symbol}")
        success = verify_data(qlib_dir, symbol, freq)

        if not success:
            logger.error(f"股票 {symbol} 数据验证失败")
            return False

        logger.info(f"股票 {symbol} 处理完成")
        return True

    except Exception as e:
        logger.error(f"处理股票 {symbol} 时发生错误: {e}")
        return False

if __name__ == "__main__":
    # 股票配置
    stocks_config = [
        {
            'symbol': '513980',
            'input_file': 'kline_513980.sh_origin.csv',
            'output_file': 'prepared_513980.csv',
            'qlib_dir': './qlib_data_513980'
        },
        {
            'symbol': '513120',
            'input_file': 'kline_513120.sh_origin.csv',
            'output_file': 'prepared_513120.csv',
            'qlib_dir': './qlib_data_513120'
        }
    ]

    freq = "5min"

    logger.info("开始处理多支股票数据转换")
    logger.info(f"待处理股票: {[config['symbol'] for config in stocks_config]}")

    success_count = 0
    total_count = len(stocks_config)

    # 逐个处理每支股票
    for stock_config in stocks_config:
        logger.info(f"\n{'='*50}")
        success = process_stock(stock_config, freq)
        if success:
            success_count += 1
        logger.info(f"{'='*50}\n")

    # 输出处理结果
    logger.info(f"数据处理完成: 成功 {success_count}/{total_count}")

    if success_count == total_count:
        logger.info("所有股票数据处理成功！")
    else:
        logger.warning(f"有 {total_count - success_count} 支股票处理失败")

    # 输出各股票的qlib数据目录
    logger.info("\n各股票qlib数据目录:")
    for stock_config in stocks_config:
        logger.info(f"  {stock_config['symbol']}: {stock_config['qlib_dir']}")
